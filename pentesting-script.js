// Enhanced Pentesting Slides Script with Performance Optimizations

document.addEventListener('DOMContentLoaded', function() {
    'use strict';

    // Performance optimization: Cache DOM elements
    const domElements = {
        slideContainer: document.getElementById('slideContainer'),
        slideIndicators: document.getElementById('slideIndicators'),
        prevBtn: document.getElementById('prevBtn'),
        nextBtn: document.getElementById('nextBtn'),
        slideArrowLeft: document.getElementById('slideArrowLeft'),
        slideArrowRight: document.getElementById('slideArrowRight'),
        scrollThumb: document.getElementById('scrollThumb'),
        currentSlideEl: document.getElementById('currentSlide'),
        totalSlidesEl: document.getElementById('totalSlides'),
        progressBar: document.getElementById('progressBar'),
        categoryFilter: document.getElementById('categoryFilter'),
        searchInput: document.getElementById('searchInput'),
        tocBtn: document.getElementById('tocBtn'),
        tocModal: document.getElementById('tocModal'),
        closeTocBtn: document.getElementById('closeTocBtn'),
        tocBody: document.getElementById('tocBody')
    };

    // Destructure for easier access
    const {
        slideContainer, slideIndicators, prevBtn, nextBtn, slideArrowLeft, slideArrowRight,
        scrollThumb, currentSlideEl, totalSlidesEl, progressBar, categoryFilter, searchInput,
        tocBtn, tocModal, closeTocBtn, tocBody
    } = domElements;

    // State variables with better initialization
    let slides = [];
    let currentSlideIndex = 0;
    let filteredSlides = [];
    let isInitialized = false;
    let debounceTimer = null;

    // Performance: RequestAnimationFrame for smooth animations
    let animationFrameId = null;

    // Cross-browser compatibility helpers
    const utils = {
        // Debounce function for performance
        debounce: function(func, wait) {
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(debounceTimer);
                    func(...args);
                };
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(later, wait);
            };
        },

        // Cross-browser event listener
        addEvent: function(element, event, handler) {
            if (element && typeof element.addEventListener === 'function') {
                element.addEventListener(event, handler);
            }
        },

        // Safe element check
        isElement: function(element) {
            return element && element.nodeType === Node.ELEMENT_NODE;
        },

        // Performance-optimized class manipulation
        toggleClass: function(element, className, force) {
            if (utils.isElement(element)) {
                if (typeof force !== 'undefined') {
                    element.classList.toggle(className, force);
                } else {
                    element.classList.toggle(className);
                }
            }
        }
    };
    
    // Enhanced Terminal typing effect with performance optimizations
    function setupTerminalEffect() {
        const terminalLines = [
            "nmap -sS -sV -p- target.com",
            "nikto -h https://target.com -Tuning x",
            "dirb https://target.com /usr/share/wordlists/dirb/common.txt",
            "sqlmap -u \"https://target.com/login.php\" --forms --dbs",
            "hydra -l admin -P /usr/share/wordlists/rockyou.txt target.com https-post-form \"/login:username=^USER^&password=^PASS^:F=Invalid\"",
            "wpscan --url https://target.com --enumerate u,vp --api-token YOUR_TOKEN",
            "gobuster dir -u https://target.com -w /usr/share/wordlists/dirbuster/directory-list-2.3-medium.txt -x php,html,js",
            "ffuf -w /usr/share/wordlists/SecLists/Discovery/Web-Content/common.txt -u https://target.com/FUZZ",
            "zap-cli quick-scan --self-contained https://target.com",
            "whatweb https://target.com -a 3"
        ];

        // Cache DOM elements for better performance
        const terminalElements = {
            body: document.querySelector('.terminal-body'),
            prompt: document.querySelector('.prompt'),
            command: document.querySelector('.command'),
            output: document.querySelector('.terminal-output')
        };

        // Validate elements exist
        if (!terminalElements.command || !terminalElements.output) {
            console.warn('Terminal elements not found, skipping terminal effect');
            return;
        }

        let currentLineIndex = 0;
        let typeInterval = null;
        let isTyping = false;
        
        function typeNextCommand() {
            if (currentLineIndex >= terminalLines.length) {
                currentLineIndex = 0;
            }

            const command = terminalLines[currentLineIndex];
            if (!terminalElements.command) return;

            terminalElements.command.textContent = '';
            isTyping = true;

            let charIndex = 0;

            // Clear any existing interval
            if (typeInterval) {
                clearInterval(typeInterval);
            }

            typeInterval = setInterval(() => {
                if (charIndex < command.length && isTyping) {
                    // Performance optimization: batch character updates
                    const batchSize = Math.random() > 0.8 ? 2 : 1; // Occasionally type 2 chars for speed variation
                    const endIndex = Math.min(charIndex + batchSize, command.length);

                    terminalElements.command.textContent = command.substring(0, endIndex);

                    // Optional: Add subtle typing sound effect (disabled by default for performance)
                    if (Math.random() > 0.9) {
                        try {
                            // Create a simple beep sound using Web Audio API
                            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                            const oscillator = audioContext.createOscillator();
                            const gainNode = audioContext.createGain();

                            oscillator.connect(gainNode);
                            gainNode.connect(audioContext.destination);

                            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                            gainNode.gain.setValueAtTime(0.01, audioContext.currentTime);
                            gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.1);

                            oscillator.start(audioContext.currentTime);
                            oscillator.stop(audioContext.currentTime + 0.1);
                        } catch (e) {
                            // Ignore audio errors
                        }
                    }

                    charIndex = endIndex;
                } else {
                    clearInterval(typeInterval);
                    isTyping = false;
                    // Use requestAnimationFrame for smooth transition
                    requestAnimationFrame(() => {
                        setTimeout(showOutput, 600);
                    });
                }
            }, 60 + Math.random() * 40); // Variable typing speed for realism
        }
        
        function showOutput() {
            if (!terminalElements.output) return;

            // Performance optimization: Use object for command outputs
            const commandOutputs = {
                0: { // nmap
                    outputs: [
                        "Starting Nmap 7.94 ( https://nmap.org )",
                        "Scanning target.com (*************) [65535 ports]",
                        "PORT     STATE SERVICE VERSION",
                        "22/tcp   open  ssh     OpenSSH 8.2p1",
                        "80/tcp   open  http    Apache httpd 2.4.41",
                        "443/tcp  open  https   Apache httpd 2.4.41",
                        "8080/tcp open  http    Tomcat 9.0.31",
                        "3306/tcp open  mysql   MySQL 5.7.33"
                    ],
                    highlightIndices: [2],
                    successIndices: [3, 4, 5, 6, 7]
                },
                1: { // nikto
                    outputs: [
                        "- Nikto v2.1.6",
                        "- Target: https://target.com/",
                        "+ Server: Apache/2.4.41",
                        "+ Cookie PHPSESSID created without the httponly flag",
                        "+ X-XSS-Protection header not defined",
                        "+ /admin/: Admin login page accessible",
                        "! OSVDB-3092: /backup/: Backup directory found"
                    ],
                    highlightIndices: [2],
                    errorIndices: [6],
                    successIndices: [3, 4, 5]
                },
                2: { // dirb
                    outputs: [
                        "DIRB v2.22",
                        "START_TIME: Wed Jun 5 10:15:22 2024",
                        "FOUND: https://target.com/admin/ (CODE:200|SIZE:1234)",
                        "FOUND: https://target.com/login/ (CODE:200|SIZE:987)",
                        "FOUND: https://target.com/backup/ (CODE:403|SIZE:301)",
                        "FOUND: https://target.com/config.php.bak (CODE:200|SIZE:1122)",
                        "FOUND: https://target.com/.git/HEAD (CODE:200|SIZE:23)"
                    ],
                    successIndices: [2, 3],
                    errorIndices: [4, 5, 6]
                },
                3: { // sqlmap
                    outputs: [
                        "sqlmap identified the following injection point(s)",
                        "Parameter: username (POST)",
                        "Type: boolean-based blind",
                        "Title: AND boolean-based blind - WHERE or HAVING clause",
                        "Payload: username=admin' AND 5587=5587 AND 'zBVA'='zBVA&password=test",
                        "available databases [5]:",
                        "- information_schema",
                        "- mysql",
                        "- performance_schema",
                        "- sys",
                        "- users_db"
                    ],
                    highlightIndices: [0, 1, 5],
                    successIndices: [2, 3, 6, 7, 8, 9, 10]
                },
                case 4: // hydra
                    outputs = [
                        "Hydra v9.4 (c) 2022 by van Hauser/THC",
                        "Hydra (https://github.com/vanhauser-thc/thc-hydra)",
                        "[DATA] max 16 tasks per 1 server",
                        "[DATA] attacking https-post-form://target.com:443/login.php",
                        "[STATUS] 364.00 tries/min, 364 tries in 00:01h, 9636 to do in 00:27h",
                        "[443][https-post-form] host: target.com   login: admin   password: admin123!",
                        "[443][https-post-form] host: target.com   login: user   password: password123"
                    ];
                    highlightIndices = [3];
                    successIndices = [5, 6];
                    break;
                case 5: // wpscan
                    outputs = [
                        "WordPress version 5.8.3 identified (Insecure, released on 2021-01-26)",
                        "WordPress theme in use: twentytwentyone v1.0",
                        "Vulnerable plugins:",
                        "contact-form-7 version 5.4.1 (CVE-2021-24284)",
                        "wp-file-manager version 6.7 (CVE-2020-25213)",
                        "Users identified:",
                        "admin (Administrator)",
                        "editor (Editor)",
                        "author (Author)"
                    ];
                    errorIndices = [0, 3, 4];
                    highlightIndices = [2, 5];
                    successIndices = [6, 7, 8];
                    break;
                case 6: // gobuster
                    outputs = [
                        "Gobuster v3.1.0",
                        "Starting gobuster in directory enumeration mode",
                        "/images              (Status: 301) [Size: 313]",
                        "/admin               (Status: 301) [Size: 312]",
                        "/js                  (Status: 301) [Size: 309]",
                        "/css                 (Status: 301) [Size: 310]",
                        "/backup              (Status: 301) [Size: 313]",
                        "/wp-admin            (Status: 301) [Size: 315]",
                        "/wp-content          (Status: 301) [Size: 317]",
                        "/config.php.bak      (Status: 200) [Size: 1122]"
                    ];
                    highlightIndices = [1];
                    successIndices = [2, 3, 4, 5, 6, 7, 8];
                    errorIndices = [9];
                    break;
                case 7: // ffuf
                    outputs = [
                        ":: Progress: [4614/4614] :: Job [1/1] :: 212 req/sec :: Duration: [0:00:21] :: Errors: 0 ::",
                        "admin                   [Status: 301, Size: 312, Words: 20]",
                        "api                     [Status: 301, Size: 310, Words: 20]",
                        "backup                  [Status: 301, Size: 313, Words: 20]",
                        ".git                    [Status: 301, Size: 310, Words: 20]",
                        "dev                     [Status: 301, Size: 310, Words: 20]",
                        "phpinfo.php             [Status: 200, Size: 83861, Words: 4925]"
                    ];
                    highlightIndices = [0];
                    successIndices = [1, 2, 3];
                    errorIndices = [4, 5, 6];
                    break;
                case 8: // zap
                    outputs = [
                        "Starting ZAP quick scan for https://target.com",
                        "Scanning https://target.com",
                        "HIGH: Cross-Site Scripting (Reflected) [10016]",
                        "URL: https://target.com/search.php?q=test",
                        "HIGH: SQL Injection [10045]",
                        "URL: https://target.com/product.php?id=1",
                        "MEDIUM: X-Frame-Options Header Not Set [10020]",
                        "LOW: Cookie No HttpOnly Flag [10010]"
                    ];
                    highlightIndices = [1];
                    errorIndices = [2, 3, 4, 5];
                    successIndices = [6, 7];
                    break;
                case 9: // whatweb
                    outputs = [
                        "https://target.com [200 OK] Apache[2.4.41], Bootstrap, Cookies[PHPSESSID], Country[UNITED STATES][US], HTML5, HTTPServer[Apache/2.4.41 (Ubuntu)], IP[*************], JQuery[3.3.1], PHP[7.4.3], Script, Title[Target Company - Home], X-Powered-By[PHP/7.4.3]"
                    ];
                    highlightIndices = [0];
                    break;
            }
            
            // Get current command output data
            const currentOutput = commandOutputs[currentLineIndex] || {
                outputs: ["Command completed successfully"],
                highlightIndices: [],
                errorIndices: [],
                successIndices: [0]
            };

            const { outputs, highlightIndices = [], errorIndices = [], successIndices = [] } = currentOutput;

            // Performance optimization: Use DocumentFragment for batch DOM updates
            const fragment = document.createDocumentFragment();

            // Add new output lines with styling
            outputs.forEach((line, index) => {
                const div = document.createElement('div');
                div.textContent = line;

                // Apply styling based on content type
                if (highlightIndices.includes(index)) {
                    div.style.color = 'var(--accent-blue)';
                    div.style.fontWeight = 'bold';
                } else if (errorIndices.includes(index)) {
                    div.style.color = 'var(--accent-red)';
                } else if (successIndices.includes(index)) {
                    div.style.color = 'var(--accent-green)';
                }

                fragment.appendChild(div);
            });

            // Add typing cursor
            const cursor = document.createElement('div');
            cursor.className = 'typing-cursor';
            cursor.textContent = '|';
            fragment.appendChild(cursor);

            // Clear previous output and add new content in one operation
            terminalElements.output.innerHTML = '';
            terminalElements.output.appendChild(fragment);

            currentLineIndex++;

            // Use requestAnimationFrame for better performance
            requestAnimationFrame(() => {
                setTimeout(typeNextCommand, 3500); // Optimized delay
            });
        }
        
        // Enhanced terminal interaction features
        function addTerminalInteractivity() {
            const terminalHeader = document.querySelector('.terminal-header');
            const terminalButtons = document.querySelectorAll('.btn');

            if (terminalHeader) {
                // Add click-to-focus functionality
                terminalHeader.addEventListener('click', () => {
                    terminalElements.body?.focus();
                });

                // Add terminal window controls
                terminalButtons.forEach((btn, index) => {
                    btn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        switch(index) {
                            case 0: // Red button - pause/resume terminal
                                if (isTyping) {
                                    clearInterval(typeInterval);
                                    isTyping = false;
                                    btn.style.backgroundColor = '#ff9500'; // Orange when paused
                                } else {
                                    typeNextCommand();
                                    btn.style.backgroundColor = '#ff5f56'; // Red when active
                                }
                                break;
                            case 1: // Yellow button - slow down
                                // Add visual feedback
                                btn.style.transform = 'scale(0.9)';
                                setTimeout(() => {
                                    btn.style.transform = 'scale(1)';
                                }, 150);
                                break;
                            case 2: // Green button - speed up
                                // Add visual feedback
                                btn.style.transform = 'scale(0.9)';
                                setTimeout(() => {
                                    btn.style.transform = 'scale(1)';
                                }, 150);
                                break;
                        }
                    });

                    // Add hover effects
                    btn.addEventListener('mouseenter', () => {
                        btn.style.boxShadow = '0 0 10px currentColor';
                    });

                    btn.addEventListener('mouseleave', () => {
                        btn.style.boxShadow = 'none';
                    });
                });
            }
        }

        // Add command history and interactive features
        function enhanceTerminalFeatures() {
            if (!terminalElements.body) return;

            // Make terminal body focusable
            terminalElements.body.setAttribute('tabindex', '0');

            // Add keyboard interaction
            terminalElements.body.addEventListener('keydown', (e) => {
                switch(e.key) {
                    case ' ': // Space to pause/resume
                        e.preventDefault();
                        if (isTyping) {
                            clearInterval(typeInterval);
                            isTyping = false;
                        } else {
                            typeNextCommand();
                        }
                        break;
                    case 'ArrowRight': // Next command
                        e.preventDefault();
                        currentLineIndex = (currentLineIndex + 1) % terminalLines.length;
                        clearInterval(typeInterval);
                        typeNextCommand();
                        break;
                    case 'ArrowLeft': // Previous command
                        e.preventDefault();
                        currentLineIndex = currentLineIndex > 0 ? currentLineIndex - 1 : terminalLines.length - 1;
                        clearInterval(typeInterval);
                        typeNextCommand();
                        break;
                    case 'r': // Restart current command
                        e.preventDefault();
                        clearInterval(typeInterval);
                        typeNextCommand();
                        break;
                }
            });

            // Add visual feedback for focus
            terminalElements.body.addEventListener('focus', () => {
                terminalElements.body.style.outline = '2px solid var(--accent-blue)';
            });

            terminalElements.body.addEventListener('blur', () => {
                terminalElements.body.style.outline = 'none';
            });
        }

        // Add copy-to-clipboard functionality
        function addCopyFeature() {
            if (!terminalElements.command) return;

            // Create copy button
            const copyButton = document.createElement('button');
            copyButton.innerHTML = '📋';
            copyButton.className = 'terminal-copy-btn';
            copyButton.title = 'Copy command to clipboard';
            copyButton.style.cssText = `
                position: absolute;
                right: 10px;
                top: 50%;
                transform: translateY(-50%);
                background: var(--accent-blue);
                border: none;
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                opacity: 0;
                transition: opacity 0.3s ease;
                z-index: 10;
            `;

            // Find terminal line container and make it relative
            const terminalLine = terminalElements.command.closest('.terminal-line');
            if (terminalLine) {
                terminalLine.style.position = 'relative';
                terminalLine.appendChild(copyButton);

                // Show copy button on hover
                terminalLine.addEventListener('mouseenter', () => {
                    copyButton.style.opacity = '1';
                });

                terminalLine.addEventListener('mouseleave', () => {
                    copyButton.style.opacity = '0';
                });

                // Copy functionality
                copyButton.addEventListener('click', async (e) => {
                    e.stopPropagation();
                    const command = terminalElements.command.textContent;

                    try {
                        await navigator.clipboard.writeText(command);
                        copyButton.innerHTML = '✅';
                        copyButton.style.background = 'var(--accent-green)';

                        setTimeout(() => {
                            copyButton.innerHTML = '📋';
                            copyButton.style.background = 'var(--accent-blue)';
                        }, 1500);
                    } catch (err) {
                        // Fallback for older browsers
                        const textArea = document.createElement('textarea');
                        textArea.value = command;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);

                        copyButton.innerHTML = '✅';
                        copyButton.style.background = 'var(--accent-green)';

                        setTimeout(() => {
                            copyButton.innerHTML = '📋';
                            copyButton.style.background = 'var(--accent-blue)';
                        }, 1500);
                    }
                });
            }
        }

        // Add terminal command suggestions
        function addCommandSuggestions() {
            const suggestions = [
                { cmd: 'nmap', desc: 'Network discovery and security auditing' },
                { cmd: 'nikto', desc: 'Web server scanner' },
                { cmd: 'dirb', desc: 'Web content scanner' },
                { cmd: 'sqlmap', desc: 'SQL injection testing tool' },
                { cmd: 'hydra', desc: 'Password cracking tool' },
                { cmd: 'wpscan', desc: 'WordPress security scanner' },
                { cmd: 'gobuster', desc: 'Directory/file brute-forcer' },
                { cmd: 'ffuf', desc: 'Fast web fuzzer' },
                { cmd: 'zap-cli', desc: 'OWASP ZAP command line' },
                { cmd: 'whatweb', desc: 'Web technology identifier' }
            ];

            // Create suggestions tooltip
            const tooltip = document.createElement('div');
            tooltip.className = 'terminal-tooltip';
            tooltip.style.cssText = `
                position: absolute;
                background: var(--darker-bg);
                border: 1px solid var(--accent-blue);
                border-radius: 6px;
                padding: 10px;
                color: var(--text-primary);
                font-size: 12px;
                max-width: 300px;
                z-index: 1000;
                opacity: 0;
                pointer-events: none;
                transition: opacity 0.3s ease;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            `;

            document.body.appendChild(tooltip);

            // Show tooltip on command hover
            if (terminalElements.command) {
                terminalElements.command.addEventListener('mouseenter', (e) => {
                    const currentCmd = terminalElements.command.textContent.split(' ')[0];
                    const suggestion = suggestions.find(s => s.cmd === currentCmd);

                    if (suggestion) {
                        tooltip.innerHTML = `<strong>${suggestion.cmd}</strong><br>${suggestion.desc}`;
                        tooltip.style.opacity = '1';

                        const rect = terminalElements.command.getBoundingClientRect();
                        tooltip.style.left = `${rect.left}px`;
                        tooltip.style.top = `${rect.bottom + 10}px`;
                    }
                });

                terminalElements.command.addEventListener('mouseleave', () => {
                    tooltip.style.opacity = '0';
                });
            }
        }

        // Initialize all enhanced features
        addTerminalInteractivity();
        enhanceTerminalFeatures();
        addCopyFeature();
        addCommandSuggestions();

        // Start the terminal effect
        typeNextCommand();
    }
    
    // Fetch slides data
    async function fetchSlides() {
        try {
            const response = await fetch('web_pentesting.json');
            if (!response.ok) {
                throw new Error('Failed to fetch slides data');
            }
            
            const rawData = await response.json();

            // Validate and sanitize JSON data
            slides = validateAndSanitizeSlides(rawData);
            filteredSlides = [...slides];
            
            // Update total slides count
            totalSlidesEl.textContent = slides.length;
            
            // Create slides and indicators
            createSlides();
            createIndicators();
            
            // Show first slide
            showSlide(0);
            
            // Setup terminal effect
            setupTerminalEffect();
        } catch (error) {
            console.error('Error fetching slides:', error);
            // Create error message safely
            slideContainer.innerHTML = '';
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = 'Failed to load slides. Please try again later.';
            slideContainer.appendChild(errorDiv);
        }
    }
    
    // HTML escape function to prevent XSS
    function escapeHtml(text) {
        if (typeof text !== 'string') {
            return '';
        }
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Sanitize input to prevent XSS in search
    function sanitizeInput(input) {
        if (typeof input !== 'string') {
            return '';
        }
        // Remove any HTML tags, script tags, and dangerous characters
        return input
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
            .replace(/<[^>]*>/g, '')
            .replace(/javascript:/gi, '')
            .replace(/on\w+\s*=/gi, '')
            .replace(/data:/gi, '')
            .substring(0, 100);
    }

    // Validate and sanitize slide data
    function validateAndSanitizeSlides(data) {
        if (!Array.isArray(data)) {
            console.error('Invalid slide data: not an array');
            return [];
        }

        return data.map(slide => {
            // Ensure all required properties exist
            const sanitizedSlide = {
                id: typeof slide.id === 'number' ? slide.id : 0,
                title: typeof slide.title === 'string' ? sanitizeInput(slide.title) : 'Untitled Slide',
                category: ['basics', 'intermediate', 'advanced'].includes(slide.category) ? slide.category : 'basics',
                content: typeof slide.content === 'string' ? sanitizeInput(slide.content) : '',
                codeExample: typeof slide.codeExample === 'string' ? slide.codeExample : '',
                image: typeof slide.image === 'string' ? slide.image : 'assets_I/images/placeholder-terminal.png'
            };

            // Handle tools array if present
            if (slide.tools && Array.isArray(slide.tools)) {
                sanitizedSlide.tools = slide.tools.map(tool =>
                    typeof tool === 'string' ? sanitizeInput(tool) : ''
                ).filter(tool => tool !== '');
            }

            // Handle references array if present
            if (slide.references && Array.isArray(slide.references)) {
                sanitizedSlide.references = slide.references.map(reference =>
                    typeof reference === 'string' ? sanitizeInput(reference) : ''
                ).filter(reference => reference !== '');
            }

            // Handle keyPoints based on format
            if (Array.isArray(slide.keyPoints)) {
                sanitizedSlide.keyPoints = slide.keyPoints.map(point => {
                    if (typeof point === 'string') {
                        return sanitizeInput(point);
                    } else if (typeof point === 'object' && point !== null) {
                        return {
                            title: typeof point.title === 'string' ? sanitizeInput(point.title) : '',
                            description: typeof point.description === 'string' ? sanitizeInput(point.description) : '',
                            example: typeof point.example === 'string' ? sanitizeInput(point.example) : ''
                        };
                    } else {
                        return '';
                    }
                });
            } else {
                sanitizedSlide.keyPoints = ['No key points available'];
            }

            return sanitizedSlide;
        });
    }

    // Create slides from data with XSS protection
    function createSlides() {
        slideContainer.innerHTML = '';

        filteredSlides.forEach((slide, index) => {
            const slideElement = document.createElement('div');
            slideElement.className = 'slide';
            slideElement.dataset.index = index;

            // Create slide content container
            const slideContent = document.createElement('div');
            slideContent.className = 'slide-content';

            // Create category badge
            const categorySpan = document.createElement('span');
            categorySpan.className = `slide-category ${escapeHtml(slide.category)}`;
            categorySpan.textContent = slide.category;
            slideContent.appendChild(categorySpan);

            // Create title
            const titleH2 = document.createElement('h2');
            titleH2.className = 'slide-title';
            titleH2.textContent = slide.title;
            slideContent.appendChild(titleH2);

            // Create description
            const descriptionP = document.createElement('p');
            descriptionP.className = 'slide-description';
            descriptionP.textContent = slide.content;
            slideContent.appendChild(descriptionP);

            // Create key points section
            const keyPointsDiv = document.createElement('div');
            keyPointsDiv.className = 'key-points';

            const keyPointsTitle = document.createElement('h3');
            keyPointsTitle.textContent = 'Key Points';
            keyPointsDiv.appendChild(keyPointsTitle);

            const keyPointsList = document.createElement('div');
            keyPointsList.className = 'key-points-list';

            slide.keyPoints.forEach(point => {
                const keyPointItem = document.createElement('div');
                keyPointItem.className = 'key-point-item';

                if (typeof point === 'string') {
                    const keyPointTitle = document.createElement('div');
                    keyPointTitle.className = 'key-point-title';
                    keyPointTitle.textContent = point;
                    keyPointItem.appendChild(keyPointTitle);
                } else {
                    const keyPointTitle = document.createElement('div');
                    keyPointTitle.className = 'key-point-title';
                    keyPointTitle.textContent = point.title;
                    keyPointItem.appendChild(keyPointTitle);

                    const keyPointDescription = document.createElement('div');
                    keyPointDescription.className = 'key-point-description';
                    keyPointDescription.textContent = point.description;
                    keyPointItem.appendChild(keyPointDescription);

                    const keyPointExample = document.createElement('div');
                    keyPointExample.className = 'key-point-example';

                    const exampleStrong = document.createElement('strong');
                    exampleStrong.textContent = 'Example: ';
                    keyPointExample.appendChild(exampleStrong);

                    const exampleText = document.createTextNode(point.example);
                    keyPointExample.appendChild(exampleText);

                    keyPointItem.appendChild(keyPointExample);
                }

                keyPointsList.appendChild(keyPointItem);
            });

            keyPointsDiv.appendChild(keyPointsList);
            slideContent.appendChild(keyPointsDiv);

            // Create code example
            const codeExample = document.createElement('pre');
            codeExample.className = 'code-example';
            codeExample.textContent = slide.codeExample;
            slideContent.appendChild(codeExample);

            // Add tools section if available
            if (slide.tools && Array.isArray(slide.tools) && slide.tools.length > 0) {
                const toolsSection = document.createElement('div');
                toolsSection.className = 'tools-section';

                const toolsTitle = document.createElement('h4');
                toolsTitle.textContent = 'Recommended Tools';
                toolsSection.appendChild(toolsTitle);

                const toolsList = document.createElement('div');
                toolsList.className = 'tools-list';

                slide.tools.forEach(tool => {
                    const toolBadge = document.createElement('span');
                    toolBadge.className = 'tool-badge';
                    toolBadge.textContent = sanitizeInput(tool);
                    toolsList.appendChild(toolBadge);
                });

                toolsSection.appendChild(toolsList);
                slideContent.appendChild(toolsSection);
            }

            // Add references section if available
            if (slide.references && Array.isArray(slide.references) && slide.references.length > 0) {
                const referencesSection = document.createElement('div');
                referencesSection.className = 'references-section';

                const referencesTitle = document.createElement('h4');
                referencesTitle.textContent = 'References';
                referencesSection.appendChild(referencesTitle);

                const referencesList = document.createElement('div');
                referencesList.className = 'references-list';

                slide.references.forEach(reference => {
                    const referenceBadge = document.createElement('span');
                    referenceBadge.className = 'reference-badge';
                    referenceBadge.textContent = sanitizeInput(reference);
                    referencesList.appendChild(referenceBadge);
                });

                referencesSection.appendChild(referencesList);
                slideContent.appendChild(referencesSection);
            }

            // Create slide image container
            const slideImageDiv = document.createElement('div');
            slideImageDiv.className = 'slide-image';

            const slideImg = document.createElement('img');
            slideImg.src = slide.image;
            slideImg.alt = slide.title;
            slideImg.onerror = function() {
                this.src = 'assets_I/images/placeholder-terminal.png';
            };
            slideImageDiv.appendChild(slideImg);

            // Append content and image to slide
            slideElement.appendChild(slideContent);
            slideElement.appendChild(slideImageDiv);

            slideContainer.appendChild(slideElement);
        });
    }
    
    // Create slide indicators
    function createIndicators() {
        slideIndicators.innerHTML = '';
        
        filteredSlides.forEach((_, index) => {
            const indicator = document.createElement('div');
            indicator.className = 'indicator';
            indicator.dataset.index = index;
            
            indicator.addEventListener('click', () => {
                showSlide(index);
            });
            
            slideIndicators.appendChild(indicator);
        });
    }
    
    // Show slide by index
    function showSlide(index) {
        if (filteredSlides.length === 0) return;
        
        // Ensure index is within bounds
        if (index < 0) index = 0;
        if (index >= filteredSlides.length) index = filteredSlides.length - 1;
        
        currentSlideIndex = index;
        
        // Update current slide display
        currentSlideEl.textContent = index + 1;
        
        // Update slides
        const slideElements = document.querySelectorAll('.slide');
        slideElements.forEach((slide, i) => {
            if (i === index) {
                slide.classList.add('active');
            } else {
                slide.classList.remove('active');
            }
        });
        
        // Update indicators
        const indicators = document.querySelectorAll('.indicator');
        indicators.forEach((indicator, i) => {
            if (i === index) {
                indicator.classList.add('active');
            } else {
                indicator.classList.remove('active');
            }
        });
        
        // Update progress bar
        const progress = ((index + 1) / filteredSlides.length) * 100;
        progressBar.style.width = `${progress}%`;
    }

    // Create table of contents
    function createTableOfContents() {
        const tocList = document.createElement('ul');
        tocList.className = 'toc-list';

        slides.forEach((slide, index) => {
            const tocItem = document.createElement('li');
            tocItem.className = 'toc-item';

            const tocLink = document.createElement('a');
            tocLink.className = 'toc-link';
            tocLink.href = '#';
            tocLink.dataset.slideIndex = index;

            // Create TOC elements safely
            const tocNumber = document.createElement('span');
            tocNumber.className = 'toc-number';
            tocNumber.textContent = slide.id;

            const tocTitle = document.createElement('span');
            tocTitle.className = 'toc-title';
            tocTitle.textContent = slide.title;

            const tocCategory = document.createElement('span');
            tocCategory.className = `toc-category ${escapeHtml(slide.category)}`;
            tocCategory.textContent = slide.category;

            tocLink.appendChild(tocNumber);
            tocLink.appendChild(tocTitle);
            tocLink.appendChild(tocCategory);

            tocLink.addEventListener('click', (e) => {
                e.preventDefault();
                showSlide(index);
                closeToc();
            });

            tocItem.appendChild(tocLink);
            tocList.appendChild(tocItem);
        });

        tocBody.innerHTML = '';
        tocBody.appendChild(tocList);
    }

    // Show table of contents
    function showToc() {
        createTableOfContents();
        tocModal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    // Close table of contents
    function closeToc() {
        tocModal.classList.remove('active');
        document.body.style.overflow = '';
    }
    
    // Enhanced search and filtering with real-time suggestions
    let searchSuggestions = [];
    let currentSuggestionIndex = -1;

    function initializeSearchSuggestions() {
        // Build search suggestions from slide content
        searchSuggestions = [];
        slides.forEach(slide => {
            // Add slide titles
            searchSuggestions.push({
                text: slide.title,
                type: 'title',
                slideId: slide.id,
                category: slide.category
            });

            // Add key terms from content
            const words = slide.content.toLowerCase().split(/\s+/);
            const keyTerms = words.filter(word =>
                word.length > 4 &&
                !['this', 'that', 'with', 'from', 'they', 'have', 'been', 'will', 'would', 'could', 'should'].includes(word)
            );

            keyTerms.forEach(term => {
                if (!searchSuggestions.find(s => s.text === term)) {
                    searchSuggestions.push({
                        text: term,
                        type: 'term',
                        slideId: slide.id,
                        category: slide.category
                    });
                }
            });

            // Add tools
            if (slide.tools) {
                slide.tools.forEach(tool => {
                    if (!searchSuggestions.find(s => s.text === tool.toLowerCase())) {
                        searchSuggestions.push({
                            text: tool.toLowerCase(),
                            type: 'tool',
                            slideId: slide.id,
                            category: slide.category
                        });
                    }
                });
            }
        });

        // Sort suggestions by relevance
        searchSuggestions.sort((a, b) => {
            if (a.type === 'title' && b.type !== 'title') return -1;
            if (b.type === 'title' && a.type !== 'title') return 1;
            if (a.type === 'tool' && b.type === 'term') return -1;
            if (b.type === 'tool' && a.type === 'term') return 1;
            return a.text.localeCompare(b.text);
        });
    }

    function createSearchSuggestionsDropdown() {
        const existingDropdown = document.querySelector('.search-suggestions');
        if (existingDropdown) {
            existingDropdown.remove();
        }

        const dropdown = document.createElement('div');
        dropdown.className = 'search-suggestions';
        dropdown.style.cssText = `
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--card-bg);
            border: 1px solid var(--card-border);
            border-top: none;
            border-radius: 0 0 8px 8px;
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            display: none;
        `;

        searchInput.parentElement.appendChild(dropdown);
        return dropdown;
    }

    function showSearchSuggestions(query) {
        const dropdown = document.querySelector('.search-suggestions') || createSearchSuggestionsDropdown();

        if (!query || query.length < 2) {
            dropdown.style.display = 'none';
            return;
        }

        const filteredSuggestions = searchSuggestions
            .filter(suggestion => suggestion.text.toLowerCase().includes(query.toLowerCase()))
            .slice(0, 8); // Limit to 8 suggestions

        if (filteredSuggestions.length === 0) {
            dropdown.style.display = 'none';
            return;
        }

        dropdown.innerHTML = '';
        filteredSuggestions.forEach((suggestion, index) => {
            const item = document.createElement('div');
            item.className = 'suggestion-item';
            item.style.cssText = `
                padding: 12px 16px;
                cursor: pointer;
                border-bottom: 1px solid rgba(66, 66, 102, 0.2);
                transition: background-color 0.2s ease;
                display: flex;
                align-items: center;
                gap: 10px;
            `;

            // Add type icon
            const icon = document.createElement('span');
            icon.style.cssText = `
                width: 20px;
                height: 20px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                font-weight: bold;
                color: white;
            `;

            switch(suggestion.type) {
                case 'title':
                    icon.style.background = 'var(--accent-blue)';
                    icon.textContent = 'T';
                    break;
                case 'tool':
                    icon.style.background = 'var(--accent-green)';
                    icon.textContent = '🔧';
                    break;
                case 'term':
                    icon.style.background = 'var(--accent-purple)';
                    icon.textContent = '#';
                    break;
            }

            const text = document.createElement('span');
            text.textContent = suggestion.text;
            text.style.flex = '1';

            const category = document.createElement('span');
            category.textContent = suggestion.category;
            category.style.cssText = `
                font-size: 11px;
                color: var(--text-muted);
                text-transform: uppercase;
                letter-spacing: 0.5px;
            `;

            item.appendChild(icon);
            item.appendChild(text);
            item.appendChild(category);

            // Hover effects
            item.addEventListener('mouseenter', () => {
                item.style.backgroundColor = 'var(--accent-blue)';
                item.style.color = 'white';
                currentSuggestionIndex = index;
            });

            item.addEventListener('mouseleave', () => {
                item.style.backgroundColor = 'transparent';
                item.style.color = 'var(--text-primary)';
            });

            // Click handler
            item.addEventListener('click', () => {
                searchInput.value = suggestion.text;
                dropdown.style.display = 'none';
                filterSlides();
            });

            dropdown.appendChild(item);
        });

        dropdown.style.display = 'block';
    }

    // Enhanced filter slides function
    function filterSlides() {
        // Sanitize inputs to prevent XSS
        const category = sanitizeInput(categoryFilter.value);
        const searchTerm = sanitizeInput(searchInput.value).toLowerCase();

        if (category === 'all' && searchTerm === '') {
            filteredSlides = [...slides];
        } else {
            filteredSlides = slides.filter(slide => {
                const categoryMatch = category === 'all' || slide.category === category;

                // Enhanced search functionality with weighted scoring
                let searchMatch = searchTerm === '';
                let searchScore = 0;

                if (searchTerm !== '') {
                    // Title match (highest weight)
                    if (slide.title.toLowerCase().includes(searchTerm)) {
                        searchMatch = true;
                        searchScore += 10;
                    }

                    // Content match (medium weight)
                    if (slide.content.toLowerCase().includes(searchTerm)) {
                        searchMatch = true;
                        searchScore += 5;
                    }

                    // Code example match (medium weight)
                    if (slide.codeExample.toLowerCase().includes(searchTerm)) {
                        searchMatch = true;
                        searchScore += 5;
                    }

                    // Tools match (high weight)
                    if (slide.tools && slide.tools.some(tool => tool.toLowerCase().includes(searchTerm))) {
                        searchMatch = true;
                        searchScore += 8;
                    }

                    // Search in keyPoints (handle both old and new format)
                    if (!searchMatch || searchScore < 5) {
                        const keyPointMatch = slide.keyPoints.some(point => {
                            if (typeof point === 'string') {
                                return point.toLowerCase().includes(searchTerm);
                            } else {
                                return point.title.toLowerCase().includes(searchTerm) ||
                                       point.description.toLowerCase().includes(searchTerm) ||
                                       point.example.toLowerCase().includes(searchTerm);
                            }
                        });

                        if (keyPointMatch) {
                            searchMatch = true;
                            searchScore += 3;
                        }
                    }
                }

                return categoryMatch && searchMatch;
            });

            // Sort by search relevance if there's a search term
            if (searchTerm) {
                filteredSlides.sort((a, b) => {
                    const aScore = calculateSearchScore(a, searchTerm);
                    const bScore = calculateSearchScore(b, searchTerm);
                    return bScore - aScore;
                });
            }
        }

        // Recreate slides and indicators
        createSlides();
        createIndicators();

        // Update total count with animation
        const newCount = filteredSlides.length;
        animateCounterUpdate(totalSlidesEl, parseInt(totalSlidesEl.textContent), newCount);

        // Show first slide or no results message
        if (filteredSlides.length > 0) {
            showSlide(0);
        } else {
            showNoResults(searchTerm, category);
        }
    }

    function calculateSearchScore(slide, searchTerm) {
        let score = 0;
        const term = searchTerm.toLowerCase();

        if (slide.title.toLowerCase().includes(term)) score += 10;
        if (slide.content.toLowerCase().includes(term)) score += 5;
        if (slide.codeExample.toLowerCase().includes(term)) score += 5;
        if (slide.tools && slide.tools.some(tool => tool.toLowerCase().includes(term))) score += 8;

        return score;
    }

    function animateCounterUpdate(element, from, to) {
        const duration = 300;
        const startTime = performance.now();

        function updateCounter(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            const current = Math.round(from + (to - from) * progress);
            element.textContent = current;

            if (progress < 1) {
                requestAnimationFrame(updateCounter);
            }
        }

        requestAnimationFrame(updateCounter);
    }

    function showNoResults(searchTerm, category) {
        slideContainer.innerHTML = '';
        const noResultsDiv = document.createElement('div');
        noResultsDiv.className = 'no-results';
        noResultsDiv.style.cssText = `
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        `;

        const title = document.createElement('h3');
        title.textContent = 'No Results Found';
        title.style.cssText = `
            color: var(--accent-red);
            margin-bottom: 20px;
            font-size: 1.5rem;
        `;

        const message = document.createElement('p');
        message.innerHTML = `No slides match your search criteria.<br>`;
        if (searchTerm) message.innerHTML += `Search term: "<strong>${searchTerm}</strong>"<br>`;
        if (category !== 'all') message.innerHTML += `Category: <strong>${category}</strong><br>`;
        message.innerHTML += `<br>Try adjusting your search or browse all slides.`;

        const clearButton = document.createElement('button');
        clearButton.textContent = 'Clear Filters';
        clearButton.className = 'nav-btn';
        clearButton.style.marginTop = '20px';
        clearButton.addEventListener('click', () => {
            searchInput.value = '';
            categoryFilter.value = 'all';
            filterSlides();
        });

        noResultsDiv.appendChild(title);
        noResultsDiv.appendChild(message);
        noResultsDiv.appendChild(clearButton);
        slideContainer.appendChild(noResultsDiv);

        slideIndicators.innerHTML = '';
        currentSlideEl.textContent = '0';
        progressBar.style.width = '0%';
    }
    
    // Event listeners
    prevBtn.addEventListener('click', () => {
        showSlide(currentSlideIndex - 1);
    });

    nextBtn.addEventListener('click', () => {
        showSlide(currentSlideIndex + 1);
    });

    // Add event listeners for slide arrows
    if (slideArrowLeft) {
        slideArrowLeft.addEventListener('click', () => {
            showSlide(currentSlideIndex - 1);
        });
    }

    if (slideArrowRight) {
        slideArrowRight.addEventListener('click', () => {
            showSlide(currentSlideIndex + 1);
        });
    }

    // Table of Contents functionality
    if (tocBtn) {
        tocBtn.addEventListener('click', showToc);
    }

    if (closeTocBtn) {
        closeTocBtn.addEventListener('click', closeToc);
    }

    // Close TOC when clicking outside
    if (tocModal) {
        tocModal.addEventListener('click', (e) => {
            if (e.target === tocModal) {
                closeToc();
            }
        });
    }

    categoryFilter.addEventListener('change', filterSlides);
    
    searchInput.addEventListener('input', function(e) {
        // Sanitize input in real-time
        const sanitized = sanitizeInput(e.target.value);
        if (e.target.value !== sanitized) {
            e.target.value = sanitized;
        }
        filterSlides();
    });
    
    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
        // Only handle keyboard navigation if not typing in search box
        if (document.activeElement !== searchInput) {
            switch(e.key) {
                case 'ArrowLeft':
                    e.preventDefault();
                    showSlide(currentSlideIndex - 1);
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    showSlide(currentSlideIndex + 1);
                    break;
                case 'Home':
                    e.preventDefault();
                    showSlide(0);
                    break;
                case 'End':
                    e.preventDefault();
                    showSlide(filteredSlides.length - 1);
                    break;
                case 'Escape':
                    e.preventDefault();
                    searchInput.value = '';
                    categoryFilter.value = 'all';
                    filterSlides();
                    break;
            }
        }
    });

    // Enhanced Touch/swipe support for mobile with improved gesture recognition
    let touchState = {
        startX: 0,
        startY: 0,
        endX: 0,
        endY: 0,
        startTime: 0,
        endTime: 0,
        isSwiping: false,
        isScrolling: false,
        initialScrollPosition: 0,
        velocity: 0,
        direction: null
    };

    // Touch gesture configuration
    const touchConfig = {
        swipeThreshold: 50,
        velocityThreshold: 0.3,
        maxSwipeTime: 300,
        scrollThreshold: 10,
        tapTimeout: 200
    };

    // Track horizontal scroll position for indicator
    function updateScrollIndicator() {
        if (!slideContainer || !scrollThumb) return;

        const maxScroll = slideContainer.scrollWidth - slideContainer.clientWidth;
        const scrollPercentage = (slideContainer.scrollLeft / maxScroll) * 100;

        // Update scroll thumb position
        scrollThumb.style.width = `${Math.min(100, Math.max(5, scrollPercentage))}%`;

        // If at the end of scrolling, make sure the thumb reaches the end
        if (slideContainer.scrollLeft >= maxScroll - 5) {
            scrollThumb.style.width = '100%';
        }
    }

    // Initialize scroll indicator
    function initScrollIndicator() {
        if (slideContainer) {
            slideContainer.addEventListener('scroll', updateScrollIndicator);
            // Initial update
            updateScrollIndicator();
        }
    }

    // Enhanced swipe detection with improved gesture recognition
    function initTouchGestures() {
        if (!slideContainer) return;

        slideContainer.addEventListener('touchstart', handleTouchStart, { passive: false });
        slideContainer.addEventListener('touchmove', handleTouchMove, { passive: false });
        slideContainer.addEventListener('touchend', handleTouchEnd, { passive: false });
        slideContainer.addEventListener('touchcancel', handleTouchCancel, { passive: false });
    }

    function handleTouchStart(e) {
        const touch = e.changedTouches[0];
        touchState.startX = touch.screenX;
        touchState.startY = touch.screenY;
        touchState.startTime = Date.now();
        touchState.initialScrollPosition = slideContainer.scrollLeft;
        touchState.isSwiping = false;
        touchState.isScrolling = false;
        touchState.direction = null;

        // Add visual feedback
        slideContainer.style.transition = 'none';
    }

    function handleTouchMove(e) {
        if (touchState.isSwiping || touchState.isScrolling) return;

        const touch = e.changedTouches[0];
        const currentX = touch.screenX;
        const currentY = touch.screenY;

        const diffX = touchState.startX - currentX;
        const diffY = touchState.startY - currentY;

        // Determine gesture direction
        const absX = Math.abs(diffX);
        const absY = Math.abs(diffY);

        if (absX > touchConfig.scrollThreshold || absY > touchConfig.scrollThreshold) {
            if (absX > absY * 1.5) {
                // Horizontal gesture - slide navigation
                touchState.direction = diffX > 0 ? 'left' : 'right';
                touchState.isSwiping = true;
                e.preventDefault();

                // Visual feedback during swipe
                const progress = Math.min(absX / touchConfig.swipeThreshold, 1);
                slideContainer.style.transform = `translateX(${-diffX * 0.3}px)`;
                slideContainer.style.opacity = 1 - (progress * 0.2);

            } else if (absY > absX * 1.5) {
                // Vertical gesture - allow normal scrolling
                touchState.direction = diffY > 0 ? 'up' : 'down';
                touchState.isScrolling = true;
            }
        }
    }

    function handleTouchEnd(e) {
        const touch = e.changedTouches[0];
        touchState.endX = touch.screenX;
        touchState.endY = touch.screenY;
        touchState.endTime = Date.now();

        // Reset visual feedback
        slideContainer.style.transition = 'all 0.3s ease';
        slideContainer.style.transform = 'translateX(0)';
        slideContainer.style.opacity = '1';

        if (touchState.isSwiping) {
            handleSwipeGesture();
        }

        // Reset touch state
        resetTouchState();
    }

    function handleTouchCancel(e) {
        // Reset visual feedback
        slideContainer.style.transition = 'all 0.3s ease';
        slideContainer.style.transform = 'translateX(0)';
        slideContainer.style.opacity = '1';

        resetTouchState();
    }

    function resetTouchState() {
        touchState.isSwiping = false;
        touchState.isScrolling = false;
        touchState.direction = null;
    }

    function handleSwipeGesture() {
        const diffX = touchState.startX - touchState.endX;
        const diffY = touchState.startY - touchState.endY;
        const swipeTime = touchState.endTime - touchState.startTime;
        const distance = Math.sqrt(diffX * diffX + diffY * diffY);

        // Calculate velocity
        touchState.velocity = distance / swipeTime;

        // Check if it's a valid swipe
        const isValidSwipe =
            Math.abs(diffX) > touchConfig.swipeThreshold &&
            Math.abs(diffX) > Math.abs(diffY) * 1.5 &&
            swipeTime < touchConfig.maxSwipeTime &&
            touchState.velocity > touchConfig.velocityThreshold;

        if (isValidSwipe) {
            // Add haptic feedback if available
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }

            if (diffX > 0) {
                // Swipe left - next slide
                showSlide(currentSlideIndex + 1);
            } else {
                // Swipe right - previous slide
                showSlide(currentSlideIndex - 1);
            }

            // Add visual feedback for successful swipe
            const feedback = document.createElement('div');
            feedback.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: var(--accent-blue);
                color: white;
                padding: 10px 20px;
                border-radius: 20px;
                font-size: 14px;
                z-index: 10000;
                pointer-events: none;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;
            feedback.textContent = diffX > 0 ? 'Next Slide →' : '← Previous Slide';
            document.body.appendChild(feedback);

            // Animate feedback
            requestAnimationFrame(() => {
                feedback.style.opacity = '1';
                setTimeout(() => {
                    feedback.style.opacity = '0';
                    setTimeout(() => {
                        document.body.removeChild(feedback);
                    }, 300);
                }, 800);
            });
        }
    }

    // Auto-play functionality (optional)
    let autoPlayInterval;
    let isAutoPlaying = false;

    function startAutoPlay() {
        if (isAutoPlaying) return;

        isAutoPlaying = true;
        autoPlayInterval = setInterval(() => {
            if (currentSlideIndex >= filteredSlides.length - 1) {
                showSlide(0);
            } else {
                showSlide(currentSlideIndex + 1);
            }
        }, 10000); // 10 seconds per slide
    }

    function stopAutoPlay() {
        if (!isAutoPlaying) return;

        isAutoPlaying = false;
        clearInterval(autoPlayInterval);
    }

    // Pause auto-play on user interaction
    [prevBtn, nextBtn, slideContainer].forEach(element => {
        element.addEventListener('click', stopAutoPlay);
    });

    // Update button states
    function updateButtonStates() {
        const isFirstSlide = currentSlideIndex === 0;
        const isLastSlide = currentSlideIndex === filteredSlides.length - 1;

        prevBtn.disabled = isFirstSlide;
        nextBtn.disabled = isLastSlide;

        // Update slide arrow states
        if (slideArrowLeft) {
            slideArrowLeft.disabled = isFirstSlide;
        }
        if (slideArrowRight) {
            slideArrowRight.disabled = isLastSlide;
        }

        // Update scroll indicator
        updateScrollIndicator();
    }

    // Enhanced showSlide function
    const originalShowSlide = showSlide;
    showSlide = function(index) {
        originalShowSlide(index);
        updateButtonStates();
    };

    // Add loading state
    function showLoading() {
        slideContainer.innerHTML = '';
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'loading';
        slideContainer.appendChild(loadingDiv);
    }

    // Initialize with loading state
    showLoading();
    fetchSlides();

    // Initialize scroll indicator after DOM is ready
    setTimeout(() => {
        initScrollIndicator();
        initTouchGestures(); // Initialize enhanced touch gestures
    }, 100);
});
