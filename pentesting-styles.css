/* Web Penetration Testing Page Specific Styles */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@300;400;500;600;700&family=Space+Grotesk:wght@300;400;500;600;700&display=swap');

:root {
    /* Cybersecurity-themed color palette */
    --dark-bg: #0a0e17;
    --darker-bg: #060a12;
    --terminal-bg: #0c0c16;
    --accent-green: #08a547;
    --accent-blue: #0084ff;
    --accent-red: #ff3860;
    --accent-yellow: #ffdd57;
    --accent-purple: #8b5cf6;
    --text-primary: #e6e6e6;
    --text-secondary: #a0a0a0;
    --text-muted: #6b7280;
    --code-bg: #1a1a2e;
    --card-bg: rgba(26, 27, 38, 0.8);
    --card-border: rgba(66, 66, 102, 0.5);
    --slide-shadow: rgba(0, 0, 0, 0.5);

    /* Professional font families for web penetration testing */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-heading: 'Space Grotesk', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;

    /* Typography scale */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;

    /* Font weights */
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    --font-extrabold: 800;
}

body {
    background-color: var(--dark-bg);
    color: var(--text-primary);
    font-family: var(--font-primary);
    font-weight: var(--font-normal);
    line-height: 1.6;
    letter-spacing: -0.01em;
}

/* Hero Section */
.pentest-hero {
    background: linear-gradient(135deg, var(--dark-bg), var(--darker-bg));
    padding: 80px 0 40px;
    position: relative;
    overflow: hidden;
}

.pentest-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('assets_I/images/cyber-grid.png');
    background-size: cover;
    opacity: 0.1;
    z-index: 0;
}

.hero-content {
    position: relative;
    z-index: 1;
    max-width: 600px;
    margin-bottom: 40px;
}

.hero-title {
    font-family: var(--font-heading);
    font-size: var(--text-5xl);
    font-weight: var(--font-extrabold);
    margin-bottom: 20px;
    color: var(--text-primary);
    text-shadow: 0 0 10px rgba(0, 132, 255, 0.3);
    letter-spacing: -0.02em;
    line-height: 1.1;
}

.hero-subtitle {
    font-family: var(--font-primary);
    font-size: var(--text-xl);
    font-weight: var(--font-normal);
    color: var(--text-secondary);
    margin-bottom: 30px;
    line-height: 1.5;
}

.hero-stats {
    display: flex;
    gap: 30px;
    margin-top: 30px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--accent-green);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Enhanced Responsive Terminal Effect */
.hero-terminal {
    background-color: var(--terminal-bg);
    border-radius: 12px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.6);
    overflow: hidden;
    width: 100%;
    max-width: 700px;
    margin: 0 auto;
    border: 1px solid rgba(66, 66, 102, 0.3);
    transition: all 0.3s ease;
}

.hero-terminal:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 50px rgba(0, 132, 255, 0.2);
}

.terminal-header {
    background: linear-gradient(135deg, #2d2d3a, #3a3a4a);
    padding: 12px 18px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(66, 66, 102, 0.3);
}

.terminal-buttons {
    display: flex;
    gap: 10px;
    align-items: center;
}

.btn {
    width: 5px;
    height: 5px;
    border-radius: 50%;
    transition: all 0.2s ease;
    cursor: pointer;
}

.btn:hover {
    transform: scale(1.1);
    box-shadow: 0 0 10px currentColor;
}

.btn.red {
    background-color: #ff5f56;
}
.btn.yellow {
    background-color: #ffbd2e;
}
.btn.green {
    background-color: #27c93f;
}

.terminal-title {
    color: var(--text-secondary);
    font-size: var(--text-sm);
    font-family: var(--font-mono);
    font-weight: var(--font-medium);
    flex: 1;
    text-align: center;
    letter-spacing: 0.5px;
}

.terminal-body {
    padding: 20px;
    font-family: var(--font-mono);
    font-size: var(--text-sm);
    font-weight: var(--font-normal);
    height: 220px;
    overflow-y: auto;
    line-height: 1.6;
    background: linear-gradient(135deg, var(--terminal-bg), #0f0f1a);
    scrollbar-width: thin;
    scrollbar-color: var(--accent-blue) transparent;
}

.terminal-body::-webkit-scrollbar {
    width: 6px;
}

.terminal-body::-webkit-scrollbar-track {
    background: transparent;
}

.terminal-body::-webkit-scrollbar-thumb {
    background-color: var(--accent-blue);
    border-radius: 3px;
}

.terminal-line {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.prompt {
    color: var(--accent-green);
    margin-right: 12px;
    font-weight: var(--font-semibold);
    text-shadow: 0 0 5px rgba(0, 255, 140, 0.3);
    flex-shrink: 0;
}

.command {
    color: var(--text-primary);
    font-weight: var(--font-medium);
    word-break: break-all;
    flex: 1;
}

.terminal-output {
    color: var(--text-secondary);
    margin-left: 24px;
    line-height: 1.6;
    margin-top: 8px;
}

.terminal-output div {
    margin-bottom: 4px;
    word-break: break-word;
}

.typing-cursor {
    display: inline-block;
    width: 10px;
    height: 18px;
    background-color: var(--accent-green);
    animation: blink 1.2s step-end infinite;
    margin-left: 2px;
    box-shadow: 0 0 8px rgba(0, 255, 140, 0.5);
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* Slide Controls */
.slide-controls {
    background-color: var(--darker-bg);
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.controls-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.slide-navigation {
    display: flex;
    align-items: center;
    gap: 15px;
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    background-color: var(--card-bg);
    color: var(--text-primary);
    border: 1px solid var(--card-border);
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    background-color: var(--accent-blue);
    color: white;
}

.slide-counter {
    font-family: 'Fira Code', monospace;
    color: var(--text-secondary);
}

.filter-controls {
    display: flex;
    gap: 15px;
    align-items: center;
}

.filter-select {
    background-color: var(--card-bg);
    color: var(--text-primary);
    border: 1px solid var(--card-border);
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box input {
    background-color: var(--card-bg);
    color: var(--text-primary);
    border: 1px solid var(--card-border);
    padding: 8px 15px 8px 35px;
    border-radius: 4px;
    width: 200px;
}

.search-box ion-icon {
    position: absolute;
    left: 10px;
    color: var(--text-secondary);
}

/* Course Overview Table */
.course-overview {
    padding: 60px 0 30px;
    background-color: var(--darker-bg);
    border-top: 1px solid rgba(66, 66, 102, 0.3);
    border-bottom: 1px solid rgba(66, 66, 102, 0.3);
}

.overview-title {
    font-family: var(--font-heading);
    font-size: var(--text-3xl);
    font-weight: var(--font-bold);
    color: var(--text-primary);
    text-align: center;
    margin-bottom: 30px;
    letter-spacing: -0.02em;
}

.overview-table-container {
    overflow-x: auto;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.overview-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background-color: var(--card-bg);
    border-radius: 8px;
    overflow: hidden;
}

.overview-table th {
    background-color: var(--terminal-bg);
    color: var(--text-primary);
    font-family: var(--font-heading);
    font-size: var(--text-base);
    font-weight: var(--font-semibold);
    text-align: left;
    padding: 16px 20px;
    border-bottom: 2px solid var(--accent-blue);
}

.overview-table td {
    padding: 16px 20px;
    border-bottom: 1px solid rgba(66, 66, 102, 0.3);
    color: var(--text-secondary);
    font-family: var(--font-primary);
    font-size: var(--text-sm);
    vertical-align: top;
}

.overview-table tr:last-child td {
    border-bottom: none;
}

.overview-table ul {
    margin: 0;
    padding-left: 20px;
}

.overview-table li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.category-cell {
    width: 150px;
}

.category-badge {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-family: var(--font-primary);
    font-size: var(--text-xs);
    font-weight: var(--font-semibold);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 8px;
}

.category-badge.basics {
    background-color: var(--accent-green);
    color: var(--dark-bg);
}

.category-badge.intermediate {
    background-color: var(--accent-blue);
    color: white;
}

.category-badge.advanced {
    background-color: var(--accent-red);
    color: white;
}

.slide-count {
    font-size: var(--text-xs);
    color: var(--text-muted);
    margin-top: 5px;
}

/* Slides Section */
.slides-section {
    padding: 40px 0;
}

.slides-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    gap: 20px;
}

.slide-container {
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 10px 30px var(--slide-shadow);
    overflow: hidden;
    min-height: 600px;
    max-height: 80vh;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--accent-blue) var(--card-bg);
    flex: 1;
    position: relative;
    touch-action: pan-y pinch-zoom;
}

/* Slide Navigation Arrows */
.slide-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 132, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 132, 255, 0.3);
}

.slide-arrow:hover {
    background: var(--accent-blue);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 132, 255, 0.4);
}

.slide-arrow:active {
    transform: translateY(-50%) scale(0.95);
}

.slide-arrow-left {
    left: -25px;
}

.slide-arrow-right {
    right: -25px;
}

.slide-arrow:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: translateY(-50%) scale(0.9);
}

/* Horizontal Scroll Indicator */
.horizontal-scroll-indicator {
    width: 100%;
    height: 6px;
    margin-top: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.scroll-track {
    width: 100%;
    height: 100%;
    position: relative;
}

.scroll-thumb {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-blue), var(--accent-green));
    border-radius: 3px;
    transition: all 0.3s ease;
    box-shadow: 0 0 10px rgba(0, 132, 255, 0.5);
}

.slide-container::-webkit-scrollbar {
    width: 8px;
}

.slide-container::-webkit-scrollbar-track {
    background: var(--card-bg);
}

.slide-container::-webkit-scrollbar-thumb {
    background-color: var(--accent-blue);
    border-radius: 4px;
}

/* Individual Slide Styles */
.slide {
    display: none;
    padding: 30px;
    animation: fadeEffect 0.5s;
}

.slide.active {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

@keyframes fadeEffect {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-content {
    display: flex;
    flex-direction: column;
}

.slide-title {
    font-family: var(--font-heading);
    font-size: var(--text-3xl);
    font-weight: var(--font-bold);
    margin-bottom: 20px;
    color: var(--accent-blue);
    letter-spacing: -0.01em;
    line-height: 1.2;
}

.slide-category {
    display: inline-block;
    background-color: var(--accent-blue);
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    font-family: var(--font-primary);
    font-size: var(--text-xs);
    font-weight: var(--font-semibold);
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.slide-category.basics { background-color: var(--accent-green); }
.slide-category.intermediate { background-color: var(--accent-blue); }
.slide-category.advanced { background-color: var(--accent-red); }

.slide-description {
    margin-bottom: 20px;
    color: var(--text-secondary);
}

.key-points {
    margin-bottom: 20px;
}

.key-points h3 {
    font-family: var(--font-heading);
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    margin-bottom: 15px;
    color: var(--text-primary);
    border-bottom: 2px solid var(--accent-blue);
    padding-bottom: 8px;
    letter-spacing: -0.01em;
}

.key-points-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.key-point-item {
    background-color: rgba(26, 27, 38, 0.5);
    border-left: 4px solid var(--accent-blue);
    padding: 18px;
    border-radius: 0 8px 8px 0;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.key-point-item:hover {
    background-color: rgba(26, 27, 38, 0.8);
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.key-point-title {
    font-family: var(--font-heading);
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--accent-blue);
    margin-bottom: 10px;
    letter-spacing: -0.01em;
    line-height: 1.3;
}

.key-point-description {
    font-family: var(--font-primary);
    font-size: var(--text-base);
    color: var(--text-secondary);
    margin-bottom: 12px;
    line-height: 1.6;
}

.key-point-example {
    background-color: var(--code-bg);
    padding: 14px;
    border-radius: 6px;
    font-family: var(--font-mono);
    font-size: var(--text-sm);
    font-weight: var(--font-normal);
    color: var(--text-primary);
    border-left: 3px solid var(--accent-green);
    line-height: 1.5;
}

.key-point-example strong {
    color: var(--accent-green);
    font-weight: var(--font-semibold);
}

/* Legacy support for old format */
.key-points ul {
    list-style-type: none;
    padding-left: 0;
}

.key-points li {
    position: relative;
    padding-left: 25px;
    margin-bottom: 8px;
}

.key-points li:before {
    content: '→';
    position: absolute;
    left: 0;
    color: var(--accent-blue);
}

.code-example {
    background-color: var(--code-bg);
    border-radius: 8px;
    padding: 20px;
    font-family: var(--font-mono);
    font-size: var(--text-sm);
    font-weight: var(--font-normal);
    overflow-x: auto;
    white-space: pre;
    color: var(--text-primary);
    border-left: 4px solid var(--accent-blue);
    line-height: 1.6;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    position: relative;
}

.code-example::before {
    content: 'Terminal Commands';
    position: absolute;
    top: -12px;
    left: 16px;
    background-color: var(--accent-blue);
    color: white;
    padding: 4px 12px;
    border-radius: 4px;
    font-family: var(--font-primary);
    font-size: var(--text-xs);
    font-weight: var(--font-semibold);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Tools and References Sections */
.tools-section, .references-section {
    margin-top: 20px;
    padding: 16px;
    background-color: rgba(26, 27, 38, 0.3);
    border-radius: 8px;
    border-left: 3px solid var(--accent-purple);
}

.tools-section h4, .references-section h4 {
    font-family: var(--font-heading);
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: 12px;
    letter-spacing: -0.01em;
}

.tools-list, .references-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tool-badge {
    background-color: var(--accent-blue);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-family: var(--font-primary);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    letter-spacing: 0.01em;
    transition: all 0.3s ease;
}

.tool-badge:hover {
    background-color: var(--accent-green);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 255, 140, 0.3);
}

.reference-badge {
    background-color: var(--accent-purple);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-family: var(--font-primary);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    letter-spacing: 0.01em;
    transition: all 0.3s ease;
}

.reference-badge:hover {
    background-color: var(--accent-yellow);
    color: var(--dark-bg);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 221, 87, 0.3);
}

.slide-image {
    display: flex;
    align-items: center;
    justify-content: center;
}

.slide-image img {
    max-width: 100%;
    max-height: 400px;
    border-radius: 6px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Slide Indicators */
.slide-indicators {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--card-border);
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator.active {
    background-color: var(--accent-blue);
    transform: scale(1.2);
}

/* Table of Contents Modal */
.toc-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.toc-modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.toc-content {
    background-color: var(--card-bg);
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.toc-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--card-border);
}

.toc-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.close-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.close-btn:hover {
    color: var(--accent-red);
    background-color: rgba(255, 56, 96, 0.1);
}

.toc-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--accent-blue) var(--card-bg);
}

.toc-body::-webkit-scrollbar {
    width: 8px;
}

.toc-body::-webkit-scrollbar-track {
    background: var(--card-bg);
}

.toc-body::-webkit-scrollbar-thumb {
    background-color: var(--accent-blue);
    border-radius: 4px;
}

.toc-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.toc-item {
    margin-bottom: 10px;
}

.toc-link {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    background-color: rgba(26, 27, 38, 0.5);
    border-radius: 6px;
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.toc-link:hover {
    background-color: rgba(26, 27, 38, 0.8);
    border-left-color: var(--accent-blue);
    transform: translateX(5px);
}

.toc-number {
    background-color: var(--accent-blue);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    margin-right: 15px;
    min-width: 30px;
    text-align: center;
}

.toc-title {
    flex: 1;
    font-weight: 500;
}

.toc-category {
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.7rem;
    text-transform: uppercase;
    font-weight: bold;
}

.toc-category.basics {
    background-color: var(--accent-green);
    color: white;
}

.toc-category.intermediate {
    background-color: var(--accent-blue);
    color: white;
}

.toc-category.advanced {
    background-color: var(--accent-red);
    color: white;
}

/* Progress Bar */
.progress-container {
    width: 100%;
    height: 4px;
    background-color: var(--card-border);
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 100;
}

.progress-bar {
    height: 100%;
    background-color: var(--accent-green);
    width: 0;
    transition: width 0.3s ease;
}

/* Error and No Results Messages */
.error-message, .no-results {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-secondary);
    font-size: 1.2rem;
}

.error-message {
    color: var(--accent-red);
}

/* Loading Animation */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.loading::after {
    content: '';
    width: 40px;
    height: 40px;
    border: 4px solid var(--card-border);
    border-top: 4px solid var(--accent-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Code Syntax Highlighting */
.code-example {
    position: relative;
}

.code-example::before {
    content: 'Terminal';
    position: absolute;
    top: -25px;
    left: 0;
    font-size: 0.8rem;
    color: var(--text-secondary);
    background-color: var(--darker-bg);
    padding: 2px 8px;
    border-radius: 4px 4px 0 0;
}

/* Slide Transitions */
.slide {
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.5s ease;
}

.slide.active {
    opacity: 1;
    transform: translateX(0);
}

/* Enhanced Button Styles */
.nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.nav-btn:disabled:hover {
    background-color: var(--card-bg);
    color: var(--text-primary);
}

/* Tooltip Styles */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--darker-bg);
    color: var(--text-primary);
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

/* Enhanced Footer Styles for Pentesting Page */
.footer {
    background: linear-gradient(135deg, var(--darker-bg), var(--dark-bg));
    border-top: 1px solid rgba(66, 66, 102, 0.3);
    padding: 60px 0 30px;
    margin-top: 60px;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: start;
    margin-bottom: 40px;
}

.footer-brand {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.footer-logo {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    object-fit: cover;
    border: 2px solid var(--accent-blue);
}

.footer-brand p {
    font-family: var(--font-primary);
    font-size: var(--text-base);
    color: var(--text-secondary);
    line-height: 1.6;
    max-width: 400px;
}

.footer-links h4 {
    font-family: var(--font-heading);
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: 20px;
    letter-spacing: -0.01em;
}

.footer-links ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.footer-links a {
    font-family: var(--font-primary);
    font-size: var(--text-base);
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
    padding: 8px 0;
    border-left: 3px solid transparent;
    padding-left: 12px;
}

.footer-links a:hover {
    color: var(--accent-blue);
    border-left-color: var(--accent-blue);
    transform: translateX(5px);
}

.footer-bottom {
    border-top: 1px solid rgba(66, 66, 102, 0.3);
    padding-top: 30px;
    text-align: center;
}

.footer-bottom p {
    font-family: var(--font-primary);
    font-size: var(--text-sm);
    color: var(--text-muted);
    margin: 0;
}

/* Enhanced Responsive Design with Improved Flexibility */

/* Large Desktop (1200px and up) - Default styles already applied */

/* Desktop (992px to 1199px) */
@media (max-width: 1200px) {
    .hero-content {
        max-width: 100%;
    }

    .pentest-hero {
        padding: 60px 0 30px;
    }

    .footer {
        padding: 50px 0 25px;
    }

    /* Adjust slide container for better desktop experience */
    .slide-container {
        min-height: 550px;
        max-height: 75vh;
    }

    /* Optimize terminal for desktop */
    .terminal-body {
        height: 200px;
    }

    /* Improved navigation for desktop */
    .controls-wrapper {
        padding: 0 20px;
    }

    .filter-controls {
        gap: 20px;
    }

    /* Better table responsiveness */
    .overview-table th,
    .overview-table td {
        padding: 14px 18px;
    }
}

@media (max-width: 992px) {
    .slide.active {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .controls-wrapper {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
        padding: 0 15px;
    }

    .slide-navigation {
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 10px;
    }

    .filter-controls {
        flex-direction: column;
        gap: 12px;
    }

    /* Enhanced mobile slide navigation with better touch targets */
    .slides-wrapper {
        flex-direction: column;
        gap: 15px;
        padding: 0 10px;
    }

    .slide-arrow {
        position: relative;
        top: auto;
        transform: none;
        width: 48px;
        height: 48px;
        font-size: 20px;
        min-width: 48px; /* Ensure minimum touch target size */
        min-height: 48px;
    }

    .slide-arrow-left,
    .slide-arrow-right {
        left: auto;
        right: auto;
    }

    .slide-navigation {
        order: 2;
    }

    .slide-container {
        order: 1;
        min-height: 500px;
        max-height: 70vh;
    }

    .slide-indicators {
        order: 3;
        max-height: 80px;
        overflow-y: auto;
        padding: 10px 0;
    }

    .horizontal-scroll-indicator {
        order: 4;
        margin-top: 10px;
    }

    .search-box {
        width: 100%;
    }

    .search-box input {
        width: 100%;
        padding: 12px 15px 12px 40px; /* Larger touch target */
        font-size: var(--text-base);
    }

    .hero-title {
        font-size: 2.5rem;
        text-align: center;
    }

    .hero-subtitle {
        text-align: center;
    }

    .terminal-body {
        height: 180px;
    }

    /* Improved navigation buttons for touch */
    .nav-btn {
        padding: 12px 16px;
        font-size: var(--text-base);
        min-height: 44px; /* iOS recommended touch target */
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .footer-brand {
        text-align: center;
        align-items: center;
    }

    .footer-brand p {
        max-width: 100%;
    }

    .footer-links {
        text-align: center;
    }

    .footer-links ul {
        align-items: center;
    }

    .footer-links a {
        border-left: none;
        border-bottom: 2px solid transparent;
        padding-left: 0;
        padding-bottom: 8px;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .footer-links a:hover {
        border-left-color: transparent;
        border-bottom-color: var(--accent-blue);
        transform: translateY(-3px);
    }

    /* Enhanced terminal responsiveness */
    .hero-terminal {
        max-width: 100%;
        margin: 0 15px;
    }

    .terminal-line {
        flex-direction: column;
        align-items: flex-start;
    }

    .command {
        margin-top: 5px;
        margin-left: 15px;
        word-break: break-word;
    }

    /* Better slide content spacing */
    .slide {
        padding: 20px;
    }

    .key-point-item {
        padding: 15px;
        margin-bottom: 15px;
    }

    /* Improved table of contents for tablets */
    .toc-content {
        width: 95%;
        max-height: 85vh;
    }

    .toc-link {
        padding: 12px 15px;
        min-height: 44px;
    }
}

@media (max-width: 768px) {
    .hero-stats {
        flex-wrap: wrap;
        justify-content: center;
        gap: 20px;
    }

    .slide-indicators {
        max-height: 100px;
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: var(--accent-blue) transparent;
    }

    .slide-indicators::-webkit-scrollbar {
        width: 4px;
    }

    .slide-indicators::-webkit-scrollbar-thumb {
        background-color: var(--accent-blue);
        border-radius: 2px;
    }

    .filter-controls {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 10px;
    }

    .filter-select {
        flex: 1;
        min-width: 140px;
        padding: 10px 12px;
        font-size: var(--text-sm);
    }

    .search-box {
        flex: 2;
        min-width: 200px;
    }

    .search-box input {
        padding: 10px 12px 10px 35px;
        font-size: var(--text-sm);
    }

    /* Enhanced terminal for tablets with better touch interaction */
    .terminal-header {
        padding: 10px 15px;
    }

    .terminal-buttons {
        gap: 8px;
    }

    .btn {
        width: 5px;
        height: 5px;
        cursor: pointer;
    }

    .terminal-title {
        font-size: var(--text-xs);
    }

    .terminal-body {
        padding: 15px;
        height: 160px;
        font-size: var(--text-xs);
        touch-action: pan-y;
    }

    .terminal-output {
        margin-left: 20px;
    }

    /* Course overview table responsiveness with better scrolling */
    .overview-table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        border-radius: 8px;
        box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.1);
    }

    .overview-table {
        min-width: 800px;
    }

    .overview-table th,
    .overview-table td {
        padding: 12px 16px;
        font-size: var(--text-xs);
    }

    /* Improved slide content for tablets */
    .slide-title {
        font-size: var(--text-2xl);
        margin-bottom: 15px;
    }

    .slide-description {
        font-size: var(--text-sm);
        line-height: 1.6;
    }

    .key-point-title {
        font-size: var(--text-lg);
    }

    .key-point-description {
        font-size: var(--text-sm);
    }

    .code-example {
        font-size: var(--text-xs);
        padding: 15px;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    /* Footer tablet styles */
    .footer {
        padding: 40px 0 20px;
    }

    .footer-brand {
        gap: 15px;
    }

    .footer-logo {
        width: 50px;
        height: 50px;
    }

    /* Better modal responsiveness */
    .toc-content {
        width: 90%;
        max-height: 80vh;
    }

    .toc-header {
        padding: 15px 20px;
    }

    .toc-body {
        padding: 15px 20px;
        max-height: 65vh;
    }

    /* Enhanced button interactions for touch */
    .nav-btn:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }

    .slide-arrow:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: var(--text-3xl);
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: var(--text-lg);
    }

    .hero-stats {
        flex-direction: column;
        gap: 15px;
    }

    .stat-number {
        font-size: var(--text-3xl);
    }

    .slide-title {
        font-size: var(--text-2xl);
    }

    .slide {
        padding: 15px;
    }

    .nav-btn {
        padding: 8px 12px;
        font-size: var(--text-sm);
    }

    /* Mobile terminal optimizations */
    .hero-terminal {
        border-radius: 8px;
        margin: 0 10px;
    }

    .terminal-header {
        padding: 8px 12px;
    }

    .terminal-buttons {
        gap: 6px;
    }

    .btn {
        width: 5px;
        height: 5px;
    }

    .terminal-title {
        font-size: 10px;
    }

    .terminal-body {
        height: 140px;
        padding: 12px;
        font-size: 11px;
    }

    .terminal-line {
        margin-bottom: 8px;
    }

    .prompt {
        margin-right: 8px;
        font-size: 11px;
    }

    .command {
        font-size: 11px;
        word-break: break-all;
    }

    .terminal-output {
        margin-left: 16px;
        font-size: 10px;
    }

    .typing-cursor {
        width: 8px;
        height: 12px;
    }

    .code-example {
        font-size: var(--text-xs);
        padding: 12px;
    }

    .filter-controls {
        flex-direction: column;
        gap: 10px;
    }

    .filter-select, .search-box {
        width: 100%;
    }

    /* Mobile course overview */
    .overview-title {
        font-size: var(--text-2xl);
        margin-bottom: 20px;
    }

    .overview-table {
        min-width: 600px;
    }

    .overview-table th,
    .overview-table td {
        padding: 8px 12px;
        font-size: 11px;
    }

    .category-cell {
        width: 100px;
    }

    .category-badge {
        padding: 4px 8px;
        font-size: 9px;
    }

    /* Mobile footer */
    .footer {
        padding: 30px 0 15px;
        margin-top: 40px;
    }

    .footer-content {
        gap: 20px;
    }

    .footer-brand {
        gap: 12px;
    }

    .footer-logo {
        width: 40px;
        height: 40px;
    }

    .footer-brand p {
        font-size: var(--text-sm);
    }

    .footer-links h4 {
        font-size: var(--text-base);
        margin-bottom: 15px;
    }

    .footer-links a {
        font-size: var(--text-sm);
        padding: 6px 0;
    }

    .footer-bottom {
        padding-top: 20px;
    }

    .footer-bottom p {
        font-size: var(--text-xs);
    }
}

@media (max-width: 480px) {
    .pentest-hero {
        padding: 30px 0 15px;
    }

    .hero-title {
        font-size: var(--text-2xl);
        margin-bottom: 15px;
    }

    .hero-subtitle {
        font-size: var(--text-base);
        margin-bottom: 20px;
    }

    .slide-controls {
        padding: 8px 0;
    }

    .slide {
        padding: 12px;
    }

    .slide-title {
        font-size: var(--text-xl);
        margin-bottom: 15px;
    }

    .stat-number {
        font-size: var(--text-2xl);
    }

    .stat-label {
        font-size: var(--text-xs);
    }

    /* Extra small mobile terminal */
    .hero-terminal {
        margin: 0 5px;
        border-radius: 6px;
    }

    .terminal-header {
        padding: 6px 10px;
    }

    .terminal-buttons {
        gap: 4px;
    }

    .btn {
        width: 5px;
        height: 5px;
    }

    .terminal-title {
        font-size: 9px;
    }

    .terminal-body {
        height: 120px;
        padding: 10px;
        font-size: 10px;
    }

    .terminal-line {
        margin-bottom: 6px;
    }

    .prompt {
        margin-right: 6px;
        font-size: 10px;
    }

    .command {
        font-size: 10px;
    }

    .terminal-output {
        margin-left: 12px;
        font-size: 9px;
    }

    .typing-cursor {
        width: 6px;
        height: 10px;
    }

    /* Extra small mobile navigation */
    .nav-btn {
        padding: 6px 10px;
        font-size: var(--text-xs);
    }

    .slide-counter {
        font-size: var(--text-xs);
    }

    /* Extra small mobile content */
    .key-point-title {
        font-size: var(--text-base);
    }

    .key-point-description {
        font-size: var(--text-sm);
    }

    .key-point-example {
        font-size: var(--text-xs);
        padding: 10px;
    }

    .code-example {
        font-size: 10px;
        padding: 10px;
    }

    .tool-badge, .reference-badge {
        font-size: var(--text-xs);
        padding: 4px 8px;
    }

    /* Extra small mobile table */
    .overview-table {
        min-width: 500px;
    }

    .overview-table th,
    .overview-table td {
        padding: 6px 8px;
        font-size: 10px;
    }

    .category-badge {
        padding: 3px 6px;
        font-size: 8px;
    }

    /* Extra small mobile footer */
    .footer {
        padding: 20px 0 10px;
        margin-top: 30px;
    }

    .footer-brand p {
        font-size: var(--text-xs);
    }

    .footer-links h4 {
        font-size: var(--text-sm);
        margin-bottom: 10px;
    }

    .footer-links a {
        font-size: var(--text-xs);
        padding: 4px 0;
    }

    .footer-bottom {
        padding-top: 15px;
    }

    .footer-bottom p {
        font-size: 10px;
    }
}
